import { useCallback } from "react";
import { useApiPost } from "./useApi";
import { SessionResponse } from "./apiConfig";
import { useStreamingAvatarSession } from "./useStreamingAvatarSession";
import { useMessageHistory } from "./useMessageHistory";
import { useAuthContext } from "../Prividers/AuthProvider";
import { AVATARS } from "@/app/lib/constants";

// Utility function to generate avatar-language identifier
const getAvatarLanguageIdentifier = (
  avatarId: string | null,
  language: string | null
): string => {
  if (!avatarId || !language) return "unknown";

  const avatar = AVATARS.find((a) => a.avatar_id === avatarId);
  if (!avatar) return "unknown";

  const avatarName = avatar.avatar_name.toLowerCase();
  const lang = language === "es" ? "spanish" : "english";

  return `${lang}_${avatarName}`;
};

export const useSessionManager = () => {
  const { sessionInfo } = useStreamingAvatarSession();
  const { messages } = useMessageHistory();
  const auth = useAuthContext();
  const sessionApi = useApiPost<SessionResponse>("/AI/CreateUserSession");

  const saveSession = useCallback(async () => {
    try {
      // Prepare session data for API call
      const avatarLanguageId = getAvatarLanguageIdentifier(
        sessionInfo.avatarId,
        sessionInfo.language
      );

      // Filter out system messages and format conversation history
      const conversationHistory = messages
        .filter(
          (msg) =>
            msg.content !== `Hi, my name is ${auth?.user?.displayName}` &&
            msg.content !== `Hola, me llamo ${auth?.user?.displayName}`
        )
        .map((msg) => ({
          sender: msg.sender,
          content: msg.content,
          timestamp: msg.id,
        }));

      const metadata = JSON.stringify({
        avatarLanguageId,
        conversationHistory,
        sessionStartTime: new Date().toISOString(),
        userDisplayName: auth?.user?.displayName,
        messageCount: conversationHistory.length,
      });

      console.log("Saving session data:", {
        sessionId: sessionInfo.sessionId,
        preferredLanguage: sessionInfo.language,
        avatarLanguageId,
        messageCount: conversationHistory.length,
      });

      // Generate a meaningful session ID if we don't have one from HeyGen
      const finalSessionId =
        sessionInfo.sessionId && !sessionInfo.sessionId.startsWith("temp_")
          ? sessionInfo.sessionId
          : `${avatarLanguageId}_${auth?.user?.username || "user"}_${Date.now()}`;

      console.log("Using session ID for API call:", finalSessionId);

      // Call the session API
      const response = await sessionApi.execute({
        data: {
          sessionId: finalSessionId,
          preferredLanguage: sessionInfo.language || "en",
          metadata: metadata,
        },
      });

      console.log("Session saved successfully:", response);
      return response;
    } catch (error) {
      console.error("Error saving session:", error);
      throw error;
    }
  }, [sessionInfo, messages, auth?.user?.displayName, sessionApi]);

  const getSessionSummary = useCallback(() => {
    const avatarLanguageId = getAvatarLanguageIdentifier(
      sessionInfo.avatarId,
      sessionInfo.language
    );

    const conversationHistory = messages.filter(
      (msg) =>
        msg.content !== `Hi, my name is ${auth?.user?.displayName}` &&
        msg.content !== `Hola, me llamo ${auth?.user?.displayName}`
    );

    return {
      sessionId: sessionInfo.sessionId,
      avatarLanguageId,
      messageCount: conversationHistory.length,
      language: sessionInfo.language,
      avatarId: sessionInfo.avatarId,
    };
  }, [sessionInfo, messages, auth?.user?.displayName]);

  return {
    saveSession,
    getSessionSummary,
    sessionInfo,
    isLoading: sessionApi.loading,
    error: sessionApi.error,
  };
};
