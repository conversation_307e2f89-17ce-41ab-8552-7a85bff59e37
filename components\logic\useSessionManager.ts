import { useCallback } from "react";
import { useApiPost } from "./useApi";
import { SessionResponse } from "./apiConfig";
import { useStreamingAvatarSession } from "./useStreamingAvatarSession";
import { useMessageHistory } from "./useMessageHistory";
import { useAuthContext } from "../Prividers/AuthProvider";
import { AVATARS } from "@/app/lib/constants";

// Utility function to generate avatar-language identifier
const getAvatarLanguageIdentifier = (
  avatarId: string | null,
  language: string | null
): string => {
  console.log("🔍 getAvatarLanguageIdentifier called with:", {
    avatarId,
    language,
  });

  if (!avatarId || !language) {
    console.log("⚠️ Missing avatarId or language, returning 'unknown'");
    return "unknown";
  }

  const avatar = AVATARS.find((a) => a.avatar_id === avatarId);
  console.log("🔍 Found avatar:", avatar);

  if (!avatar) {
    console.log("⚠️ Avatar not found in AVATARS array, returning 'unknown'");
    return "unknown";
  }

  const avatarName = avatar.avatar_name.toLowerCase();
  const lang = language === "es" ? "spanish" : "english";
  const result = `${lang}_${avatarName}`;

  console.log("✅ Generated avatar language identifier:", result);
  return result;
};

export const useSessionManager = () => {
  const { sessionInfo } = useStreamingAvatarSession();
  const { messages } = useMessageHistory();
  const auth = useAuthContext();
  const sessionApi = useApiPost<SessionResponse>("/AI/CreateUserSession");

  const saveSession = useCallback(async () => {
    try {
      // Validate session info
      if (!sessionInfo) {
        console.warn("No session info available for saving");
        return null;
      }

      // Only save if we have a real HeyGen session ID (not temp or generated)
      if (
        !sessionInfo.sessionId ||
        sessionInfo.sessionId.startsWith("temp_") ||
        sessionInfo.sessionId.includes("_john.keating@") ||
        sessionInfo.sessionId.includes("_user_")
      ) {
        console.log(
          "⚠️ Skipping session save - no real HeyGen session ID available:",
          sessionInfo.sessionId
        );
        return null;
      }

      // Debug session info
      console.log("🔍 Session info in saveSession:", sessionInfo);

      // Prepare session data for API call
      const avatarLanguageId = getAvatarLanguageIdentifier(
        sessionInfo.avatarId,
        sessionInfo.language
      );

      // Filter out system messages and format conversation history
      const conversationHistory = (messages || [])
        .filter(
          (msg) =>
            msg &&
            msg.content &&
            msg.content !== `Hi, my name is ${auth?.user?.displayName}` &&
            msg.content !== `Hola, me llamo ${auth?.user?.displayName}`
        )
        .map((msg) => ({
          sender: msg.sender,
          content: msg.content,
          timestamp: msg.id,
        }));

      let metadata;
      try {
        metadata = JSON.stringify({
          avatarLanguageId,
          conversationHistory,
          sessionStartTime: new Date().toISOString(),
          userDisplayName: auth?.user?.displayName,
          messageCount: conversationHistory.length,
        });
      } catch (jsonError) {
        console.error("Error serializing metadata:", jsonError);
        metadata = JSON.stringify({
          avatarLanguageId,
          messageCount: conversationHistory.length,
          error: "Failed to serialize conversation history",
        });
      }

      console.log("Saving session data:", {
        sessionId: sessionInfo.sessionId,
        preferredLanguage: sessionInfo.language,
        avatarLanguageId,
        messageCount: conversationHistory.length,
      });

      // Use the real HeyGen session ID directly
      console.log(
        "Using HeyGen session ID for API call:",
        sessionInfo.sessionId
      );

      // Call the session API
      const response = await sessionApi.execute({
        data: {
          sessionId: sessionInfo.sessionId,
          preferredLanguage: sessionInfo.language || "en",
          metadata: metadata,
        },
      });

      console.log("Session saved successfully:", response);
      return response;
    } catch (error) {
      console.error("Error saving session:", error);
      throw error;
    }
  }, [sessionInfo, messages, auth?.user?.displayName, sessionApi]);

  const getSessionSummary = useCallback(() => {
    const avatarLanguageId = getAvatarLanguageIdentifier(
      sessionInfo.avatarId,
      sessionInfo.language
    );

    const conversationHistory = messages.filter(
      (msg) =>
        msg.content !== `Hi, my name is ${auth?.user?.displayName}` &&
        msg.content !== `Hola, me llamo ${auth?.user?.displayName}`
    );

    return {
      sessionId: sessionInfo.sessionId,
      avatarLanguageId,
      messageCount: conversationHistory.length,
      language: sessionInfo.language,
      avatarId: sessionInfo.avatarId,
    };
  }, [sessionInfo, messages, auth?.user?.displayName]);

  return {
    saveSession,
    getSessionSummary,
    sessionInfo,
    isLoading: sessionApi.loading,
    error: sessionApi.error,
  };
};
