name: Deploy

on:
  push:
    branches:
      - main # deploy on push to main branch
  workflow_dispatch: # allow manual trigger

jobs:
  Deploy:
    name: Deploy to EC2
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Build & Deploy
        env:
          PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          HOSTNAME: ${{ secrets.SSH_HOST }}
          USER_NAME: ${{ secrets.USER_NAME }}
        run: |
          # Convert the multiline private key properly
          echo "$PRIVATE_KEY" | tr -d '\r' | sed 's/\\n/\n/g' > private_key
          chmod 600 private_key

          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOSTNAME} << 'EOF'
            set -e

            cd Ai-Interactive-Avatar

            echo "Pulling latest code..."
            git pull

            echo "Stopping PM2 app if running..."
            pm2 kill || true

            echo "Cleaning old build..."
            rm -rf .next

            echo "Installing dependencies..."
            yarn install --frozen-lockfile

            echo "Building Next.js..."
            yarn build

            echo "Starting app with PM2..."
            pm2 start yarn --name "Ai-Interactive-Avatar" -- start

            pm2 save
          EOF
