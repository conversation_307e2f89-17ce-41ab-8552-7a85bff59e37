import StreamingAvatar, {
  ConnectionQuality,
  StartAvatarRequest,
  StreamingEvents,
} from "@heygen/streaming-avatar";
import { useCallback } from "react";

import {
  StreamingAvatarSessionState,
  useStreamingAvatarContext,
} from "./context";
import { useVoiceChat } from "./useVoiceChat";
import { useMessageHistory } from "./useMessageHistory";

export const useStreamingAvatarSession = () => {
  const {
    avatarRef,
    basePath,
    sessionState,
    setSessionState,
    stream,
    setStream,
    setIsListening,
    setIsUserTalking,
    setIsAvatarTalking,
    setConnectionQuality,
    handleUserTalkingMessage,
    handleStreamingTalkingMessage,
    handleEndMessage,
    clearMessages,
    sessionInfo,
    setSessionInfo,
  } = useStreamingAvatarContext();
  const { stopVoiceChat } = useVoiceChat();

  useMessageHistory();

  const init = useCallback(
    (token: string) => {
      avatarRef.current = new StreamingAvatar({
        token,
        basePath: basePath,
      });

      return avatarRef.current;
    },
    [basePath, avatarRef]
  );

  const handleStream = useCallback(
    ({ detail }: { detail: MediaStream }) => {
      setStream(detail);
      setSessionState(StreamingAvatarSessionState.CONNECTED);

      // Try to get session ID when stream is ready
      setTimeout(() => {
        const streamSessionId =
          avatarRef.current?.sessionId ||
          avatarRef.current?.session_id ||
          avatarRef.current?._sessionId;

        if (streamSessionId) {
          console.log(
            "✅ Found session ID from stream ready event:",
            streamSessionId
          );
          setSessionInfo((prev) => ({
            ...prev,
            sessionId: streamSessionId,
          }));
        }
      }, 500); // Small delay to ensure session is fully initialized
    },
    [setSessionState, setStream, setSessionInfo, avatarRef]
  );

  const stop = useCallback(async () => {
    try {
      // Remove event listeners first to prevent further events
      avatarRef.current?.off(StreamingEvents.STREAM_READY, handleStream);
      avatarRef.current?.off(StreamingEvents.STREAM_DISCONNECTED, stop);

      // Clear UI state
      clearMessages();
      setIsListening(false);
      setIsUserTalking(false);
      setIsAvatarTalking(false);
      setStream(null);

      // Clear session info
      setSessionInfo({ sessionId: null, avatarId: null, language: null });

      // Stop voice chat
      try {
        stopVoiceChat();
      } catch (voiceChatError) {
        console.warn("Error stopping voice chat:", voiceChatError);
      }

      // Stop avatar session with timeout protection
      if (avatarRef.current) {
        const stopPromise = avatarRef.current.stopAvatar();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Stop avatar timeout")), 5000)
        );

        try {
          await Promise.race([stopPromise, timeoutPromise]);
        } catch (stopError) {
          console.error("Error stopping avatar:", stopError);
          // Continue with cleanup even if stop fails
        }
      }

      setSessionState(StreamingAvatarSessionState.INACTIVE);
    } catch (error) {
      console.error("Error in stop function:", error);
      // Ensure we always set to inactive state
      setSessionState(StreamingAvatarSessionState.INACTIVE);
      throw error; // Re-throw to let caller handle
    }
  }, [
    handleStream,
    setSessionState,
    setStream,
    avatarRef,
    setIsListening,
    stopVoiceChat,
    clearMessages,
    setIsUserTalking,
    setIsAvatarTalking,
  ]);

  const start = useCallback(
    async (config: StartAvatarRequest, token?: string) => {
      if (sessionState !== StreamingAvatarSessionState.INACTIVE) {
        throw new Error("There is already an active session");
      }

      if (!avatarRef.current) {
        if (!token) {
          throw new Error("Token is required");
        }
        init(token);
      }

      if (!avatarRef.current) {
        throw new Error("Avatar is not initialized");
      }

      setSessionState(StreamingAvatarSessionState.CONNECTING);
      avatarRef.current.on(StreamingEvents.STREAM_READY, handleStream);
      avatarRef.current.on(StreamingEvents.STREAM_DISCONNECTED, stop);
      avatarRef.current.on(
        StreamingEvents.CONNECTION_QUALITY_CHANGED,
        ({ detail }: { detail: ConnectionQuality }) =>
          setConnectionQuality(detail)
      );
      avatarRef.current.on(StreamingEvents.USER_START, () => {
        setIsUserTalking(true);
      });
      avatarRef.current.on(StreamingEvents.USER_STOP, () => {
        setIsUserTalking(false);
      });
      avatarRef.current.on(StreamingEvents.AVATAR_START_TALKING, () => {
        setIsAvatarTalking(true);
      });
      avatarRef.current.on(StreamingEvents.AVATAR_STOP_TALKING, () => {
        setIsAvatarTalking(false);
      });
      avatarRef.current.on(
        StreamingEvents.USER_TALKING_MESSAGE,
        handleUserTalkingMessage
      );
      avatarRef.current.on(
        StreamingEvents.AVATAR_TALKING_MESSAGE,
        handleStreamingTalkingMessage
      );
      avatarRef.current.on(StreamingEvents.USER_END_MESSAGE, handleEndMessage);
      avatarRef.current.on(
        StreamingEvents.AVATAR_END_MESSAGE,
        handleEndMessage
      );

      // Try using newSession first, which should return detailed session info
      let sessionResponse;
      try {
        console.log(
          "🔄 Attempting to create session with newSession method..."
        );
        sessionResponse = await avatarRef.current.newSession(config);
        console.log("✅ newSession response:", sessionResponse);
      } catch (newSessionError) {
        console.warn(
          "⚠️ newSession failed, falling back to createStartAvatar:",
          newSessionError
        );
        sessionResponse = await avatarRef.current.createStartAvatar(config);
      }

      // Debug: Log the full response to understand its structure
      console.log("Full session response from HeyGen:", sessionResponse);
      console.log("Session response type:", typeof sessionResponse);
      console.log(
        "Session response keys:",
        sessionResponse ? Object.keys(sessionResponse) : "null"
      );

      // Store session information if available
      let sessionId = null;

      // Try different possible property names for session ID
      if (sessionResponse) {
        sessionId =
          sessionResponse.session_id ||
          sessionResponse.sessionId ||
          sessionResponse.id ||
          sessionResponse.data?.session_id ||
          sessionResponse.data?.sessionId;
      }

      if (sessionId) {
        setSessionInfo({
          sessionId: sessionId,
          avatarId: config.avatarName || null,
          language: config.language || null,
        });
        console.log("✅ Session info stored successfully:", {
          sessionId: sessionId,
          avatarId: config.avatarName,
          language: config.language,
        });
      } else {
        console.warn(
          "⚠️ No session ID found in response. Checking avatar instance..."
        );

        // Try to get session ID from avatar instance properties
        const avatarSessionId =
          avatarRef.current?.sessionId ||
          avatarRef.current?.session_id ||
          avatarRef.current?._sessionId;

        if (avatarSessionId) {
          console.log(
            "✅ Found session ID in avatar instance:",
            avatarSessionId
          );
          setSessionInfo({
            sessionId: avatarSessionId,
            avatarId: config.avatarName || null,
            language: config.language || null,
          });
        } else {
          console.warn(
            "⚠️ No session ID found anywhere. Setting temporary session info."
          );
          // Set session info without session ID for now
          setSessionInfo({
            sessionId: `temp_${Date.now()}`, // Temporary ID
            avatarId: config.avatarName || null,
            language: config.language || null,
          });
        }
      }

      // Add a delayed check for session ID in case it's populated later
      setTimeout(() => {
        if (!sessionId) {
          const delayedSessionId =
            avatarRef.current?.sessionId ||
            avatarRef.current?.session_id ||
            avatarRef.current?._sessionId;

          if (delayedSessionId) {
            console.log("✅ Found session ID after delay:", delayedSessionId);
            setSessionInfo((prev) => ({
              ...prev,
              sessionId: delayedSessionId,
            }));
          }
        }
      }, 2000); // Check again after 2 seconds

      return avatarRef.current;
    },
    [
      init,
      handleStream,
      stop,
      setSessionState,
      avatarRef,
      sessionState,
      setConnectionQuality,
      setIsUserTalking,
      handleUserTalkingMessage,
      handleStreamingTalkingMessage,
      handleEndMessage,
      setIsAvatarTalking,
    ]
  );

  return {
    avatarRef,
    sessionState,
    stream,
    sessionInfo,
    initAvatar: init,
    startAvatar: start,
    stopAvatar: stop,
  };
};
